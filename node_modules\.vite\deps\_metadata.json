{"hash": "54e0e118", "configHash": "5938a716", "lockfileHash": "89f5c4cb", "browserHash": "b69cf23f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "420465ec", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "eebb72b0", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "32cf<PERSON><PERSON>", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "445f37bd", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "01fc04e1", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "04d8a546", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "b3b5ff03", "needsInterop": false}, "lenis": {"src": "../../lenis/dist/lenis.mjs", "file": "lenis.js", "fileHash": "0a42c608", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "01474e1c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "9877a1df", "needsInterop": true}}, "chunks": {"chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}